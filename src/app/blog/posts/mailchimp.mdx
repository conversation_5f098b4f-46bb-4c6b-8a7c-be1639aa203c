---
title: "Set up Mailchimp and collect emails"
summary: "Set up the Mailchimp newsletter block for your Magic Portfolio."
publishedAt: "2025-04-16"
tag: "Magic Portfolio"
---

## Mailchimp account

Sign up for a [Mailchimp account](https://mailchimp.com) and create a new list if you don't have one.

## Embed form

Create a new [embed form](https://mailchimp.com/help/add-a-signup-form-to-your-website/) and copy the form URL to your Magic Portfolio config.

<CodeBlock
    highlight="2"
    codes={[
  {
    code:
`const mailchimp = {
    action: 'https://url/subscribe/post?parameters',
    effects: {
      ...
    }
};`, 
    language: "tsx",
    label: "src/app/resources/config.js"
  }
]} />

## Newsletter content

You can modify the headline and description in the `content.js` file.

<CodeBlock
    highlight="2"
    codes={[
  {
    code:
`const newsletter = {
    display: true,
    title: <>Subscribe to {person.firstName}'s Newsletter</>,
    description: (
      <>
        I occasionally write about design, technology, and share thoughts on the intersection of creativity and engineering.
      </>
    ),
};`, 
    language: "tsx",
    label: "src/app/resources/content.js"
  }
]} />

## Background effect

There's a pre-configured background in `Mailchimp.tsx` that you can modify in the `mailchimp` object. Set graphic elements such as gradient, dots, lines, and grid and configure their appearance for the newsletter block.

<CodeBlock
    codeHeight={24}
    marginBottom="16"
    codes={[
  {
    code:
`const mailchimp = {
    action: 'https://url/subscribe/post?parameters',
    effects: {
      mask: {
        cursor: false,
        x: 50,
        y: 0,
        radius: 100
      },
      gradient: {
        display: true,
        x: 50,
        y: -25,
        width: 100,
        height: 100,
        tilt: 0,
        colorStart: 'accent-background-strong',
        colorEnd: 'static-transparent',
        opacity: 50
      },
      dots: {
        display: true,
        size: 2,
        color: 'brand-on-background-weak',
        opacity: 20
      },
      lines: {
        display: false,
        color: 'neutral-alpha-weak',
        opacity: 100
      },
      grid: {
        display: false,
        color: 'neutral-alpha-weak',
        opacity: 100,
        width: 'var(--static-space-32)',
        height: 'var(--static-space-32)'
      }
  }
}`, 
    language: "tsx",
    label: "src/app/resources/config.js"
  }
]} />