---
title: "Create password protected pages"
summary: "Create password protected pages through the RouteGuard component."
publishedAt: "2025-04-17"
tag: "Magic Portfolio"
---

## Add protected pages

To enable password protection for specific pages, add the page paths you want to protect to the `protectedRoutes` object in the `resources/config.js` file.

The `RouteGuard` component will automatically handle access control for these pages, requiring password authentication before allowing access.

<CodeBlock
    marginBottom="16"
    highlight="2"
    codes={[
  {
    code:
`const protectedRoutes = {
  '/work/once-ui': true
};`,
    language: "tsx",
    label: "src/app/resources/config.js"
  }
]} />

The code above will ensure that the `/work/once-ui` page is only accessible after providing a password. 

## Set a password

The password can be set in the `.env.local` file for local development and as an [environment variable](https://vercel.com/docs/environment-variables) for production.