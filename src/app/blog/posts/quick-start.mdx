---
title: "Quick start with Magic Portfolio"
summary: "Magic Portfolio is a comprehensive, MDX-based, SEO-friendly, responsive portfolio template built with Once UI and Next.js."
image: "/images/og/home.jpg"
publishedAt: "2025-04-23"
tag: "Magic Portfolio"
---

## About

Magic Portfolio is a comprehensive, MDX-based, SEO-friendly, responsive portfolio template built with Once UI and Next.js.

## License

Magic Portfolio is licensed under the [CC BY-NC 4.0](https://creativecommons.org/licenses/by-nc/4.0/). You can only use it for personal purposes, and you must attribute the original work. The attribution is added in the footer by default, but you can place it in any other, visible part of your site.

Subscribe to the [Once UI Pro plan](https://once-ui.com/pricing) to extend the license to [Dopler CC](https://once-ui.com/products/magic-portfolio).

## Quick start

Clone the git repository:

<CodeBlock compact marginBottom="16" codes={[
  {
    code: "git clone https://github.com/once-ui-system/magic-portfolio.git",
    language: "bash"
  }
]} />

Install the necessary dependencies:

<CodeBlock compact marginBottom="16" codes={[
  {
    code: "npm install",
    language: "bash"
  }
]} />

Start the local development server:

<CodeBlock compact marginBottom="16" codes={[
  {
    code: "npm run dev",
    language: "bash"
  }
]} />