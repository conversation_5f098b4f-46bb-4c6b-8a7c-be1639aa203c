@use "../breakpoints.scss" as breakpoints;

.hover {
    transition: var(--transition-micro-medium);

    &:hover {
        transform: translateX(var(--static-space-4));
    }
}

.avatar {
    position: sticky;
    height: fit-content;
    top: var(--static-space-64);
}

@media (max-width: breakpoints.$s) {
    .avatar {
        top: auto;
    }

    .textAlign {
        text-align: center;
    }

    .blockAlign {
        align-self: center;
    }
}