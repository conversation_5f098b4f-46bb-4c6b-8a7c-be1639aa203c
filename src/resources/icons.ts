import { IconType } from "react-icons";

import {
  HiArrowUpRight,
  HiO<PERSON>lineLink,
  HiArrowTopRightOnSquare,
  HiEnvelope,
  HiCalendarDays,
  HiArrowRight,
  HiOutlineEye,
  HiOutlineEyeSlash,
  HiOutlineDocument,
  HiOutlineGlobeAsiaAustralia,
  HiOutlineRocketLaunch,
} from "react-icons/hi2";

import {
  PiHouseDuotone,
  PiUserCircleDuotone,
  PiGridFourDuotone,
  PiBookBookmarkDuotone,
  PiImageDuotone,
} from "react-icons/pi";

import { FaDiscord, FaGithub, FaLinkedin, FaX, FaThreads } from "react-icons/fa6";

export const iconLibrary: Record<string, IconType> = {
  arrowUpRight: HiArrowUpRight,
  arrowRight: HiArrowRight,
  email: HiEnvelope,
  globe: HiOutlineGlobeAsiaAustralia,
  person: PiUserCircleDuotone,
  grid: PiGridFourDuotone,
  book: <PERSON>B<PERSON><PERSON><PERSON>markDuotone,
  openLink: HiOutlineLink,
  calendar: Hi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  home: <PERSON>House<PERSON>uo<PERSON>,
  gallery: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  discord: FaD<PERSON>rd,
  eye: HiOutlineEye,
  eyeOff: HiOutlineEyeSlash,
  github: FaGithub,
  linkedin: FaLinkedin,
  x: FaX,
  threads: FaThreads,
  arrowUpRightFromSquare: HiArrowTopRightOnSquare,
  document: HiOutlineDocument,
  rocket: HiOutlineRocketLaunch
};

export type IconLibrary = typeof iconLibrary;
export type IconName = keyof IconLibrary;